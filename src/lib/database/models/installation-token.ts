import mongoose, { Schema, Document } from 'mongoose';

export interface IInstallationToken extends Document {
  _id: string;
  installation_id: string;
  token: string;
  expires_at: Date;
  created_at: Date;
  updated_at: Date;
}

const InstallationTokenSchema = new Schema<IInstallationToken>(
  {
    installation_id: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    token: {
      type: String,
      required: true,
    },
    expires_at: {
      type: Date,
      required: true,
      index: true, // For efficient expiry queries
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

// TTL index to automatically delete expired tokens
InstallationTokenSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });

export const InstallationToken = mongoose.models.InstallationToken || 
  mongoose.model<IInstallationToken>('InstallationToken', InstallationTokenSchema);
